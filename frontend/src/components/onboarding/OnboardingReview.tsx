import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { prevStep } from "../../redux/slices/onboardingSlice.ts";
import { useState, useEffect } from "react";
import { createMerchant, type CreatePayrixMerchantRequest } from "../../services/api.ts";
import { type AxiosError } from "axios";
import { toast } from "sonner";

const OnboardingReview = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitResult, setSubmitResult] = useState<{ data: { userAccount: { created: boolean; username: string; email: string } } } | null>(null);
  const [clientIp, setClientIp] = useState<string>("127.0.0.1");
  const [tcAttestation, setTcAttestation] = useState(false);
  const [visaDisclosure, setVisaDisclosure] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{
    tcAttestation?: string;
    visaDisclosure?: string;
  }>({});

  // Get client IP on component mount
  useEffect(() => {
    const getClientIp = async () => {
      try {
        const response = await fetch("https://api.ipify.org?format=json");
        const data = await response.json();
        setClientIp(data.ip);
      } catch (error) {
        console.warn("Failed to get client IP, using default:", error);
        // Keep default 127.0.0.1
      }
    };
    getClientIp();
  }, []);

  const handleSubmit = async () => {
    // Clear previous validation errors
    setValidationErrors({});

    // Validate compliance checkboxes
    const errors: { tcAttestation?: string; visaDisclosure?: string } = {};

    if (!tcAttestation) {
      errors.tcAttestation = "You must accept the Terms and Conditions to proceed.";
    }

    if (!visaDisclosure) {
      errors.visaDisclosure = "You must acknowledge the Visa disclosure to proceed.";
    }

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      toast.error("Please accept all required terms and disclosures before submitting.");
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Convert form data to Payrix format
      const merchantData: CreatePayrixMerchantRequest = {
        // Top-level entity fields
        name: formData.name || "",
        address1: formData.address1 || "",
        address2: formData.address2,
        city: formData.city || "",
        state: formData.state || "",
        zip: formData.zip || "",
        country: formData.country || "USA",
        phone: formData.phone || "",
        email: formData.email || "",
        ein: formData.ein?.replace(/\D/g, "") || "",
        website: formData.website || "https://nowebsite.com",
        type: 2, // Always 2 for merchant
        public: formData.type === 2 ? 1 : 0, // Corporation = public, others = private
        mcc: formData.merchant?.mcc || "",
        status: 1, // Active status
        tcVersion: "1.0",
        tcDate: (() => {
          // Generate YYYYMMDDHHMM format for current date/time
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, "0");
          const day = String(now.getDate()).padStart(2, "0");
          const hour = String(now.getHours()).padStart(2, "0");
          const minute = String(now.getMinutes()).padStart(2, "0");
          return `${year}${month}${day}${hour}${minute}`;
        })(),
        clientIp: clientIp,
        currency: "USD",
        tcAttestation: tcAttestation ? 1 : 0,
        visaDisclosure: visaDisclosure ? 1 : 0,
        disclosureIP: clientIp,
        disclosureDate: new Date().toISOString().slice(0, 10).replace(/-/g, ""), // YYYYMMDD format
        merchantIp: clientIp,

        // User account creation fields
        createAccount: formData.createAccount || false,
        ...(formData.createAccount && {
          username: formData.username || "",
          password: formData.password || "",
        }),

        // Bank account information
        accounts: [
          {
            primary: 1,
            currency: "USD",
            account: {
              method: formData.accounts?.[0]?.account?.method || 10,
              number: formData.accounts?.[0]?.account?.number?.replace(/\D/g, "") || "",
              routing: formData.accounts?.[0]?.account?.routing?.replace(/\D/g, "") || "",
            },
          },
        ],

        // Nested merchant object
        merchant: {
          dba: formData.merchant?.dba || formData.name || "",
          new: 1,
          mcc: formData.merchant?.mcc || "",
          status: "1", // String, not number (per interface)
          annualCCSales: formData.merchant?.annualCCSales || 0, // Payrix only accepts 0 for this field
          avgTicket: formData.merchant?.avgTicket || 50, // Default $50 if not provided
          established: formData.merchant?.established,
          members:
            formData.merchant?.members?.map((member) => ({
              title: member.title || "",
              first: member.first || "",
              middle: member.middle || "",
              last: member.last || "",
              ssn: member.ssn?.replace(/\D/g, ""),
              dob: (() => {
                // Keep date in YYYYMMDD format as required by Payrix
                if (!member.dob) return "";

                const dateStr = member.dob.replace(/\D/g, "");

                // If it's 8 digits, ensure it's in YYYYMMDD format
                if (dateStr.length === 8) {
                  // Check if first 4 digits look like a year (19xx or 20xx)
                  const firstFour = dateStr.substring(0, 4);
                  if (firstFour.startsWith("19") || firstFour.startsWith("20")) {
                    // Already in YYYYMMDD format - keep as is
                    return dateStr;
                  } else {
                    // It's MMDDYYYY format, convert to YYYYMMDD
                    const month = dateStr.substring(0, 2);
                    const day = dateStr.substring(2, 4);
                    const year = dateStr.substring(4, 8);
                    return `${year}${month}${day}`;
                  }
                } else if (member.dob.includes("-") || member.dob.includes("/")) {
                  // Parse common date formats and convert to YYYYMMDD
                  const date = new Date(member.dob);
                  if (!isNaN(date.getTime())) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, "0");
                    const day = String(date.getDate()).padStart(2, "0");
                    return `${year}${month}${day}`;
                  }
                }

                return dateStr;
              })(),
              dl: member.dl,
              dlstate: member.dlstate,
              ownership: member.ownership || 10000,
              significantResponsibility: member.significantResponsibility || 1,
              politicallyExposed: member.politicallyExposed || 0,
              email: member.email || "",
              phone: member.phone?.replace(/\D/g, "") || "",
              primary: member.primary || "1",
              address1: member.address1 || "",
              address2: member.address2,
              city: member.city || "",
              state: member.state || "",
              zip: member.zip || "",
              country: member.country || "USA",
            })) || [],
        },
      };

      const result = await createMerchant(merchantData);

      console.log("Onboarding submitted successfully:", result);
      toast.success("Merchant onboarding completed successfully!", {
        description: "Your application has been submitted for review.",
      });
      setSubmitResult(result as unknown as { data: { userAccount: { created: boolean; username: string; email: string } } });
      setSubmitSuccess(true);

      // Redirect to home page after success
      // setTimeout(() => {
      //   window.location.href = "/";
      // }, 2000);
    } catch (error) {
      console.error("Submission error:", error);
      const axiosError = error as AxiosError<{ message?: string; error?: string }>;
      const message = axiosError.response?.data?.message || axiosError.response?.data?.error || axiosError.message || "An unexpected error occurred";
      setSubmitError(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatSSN = (ssn: string) => {
    if (!ssn) return "";
    const digits = ssn.replace(/\D/g, "");
    return `***-**-${digits.slice(-4)}`;
  };

  const formatAccountNumber = (accountNumber: string) => {
    if (!accountNumber) return "";
    const digits = accountNumber.replace(/\D/g, "");
    return `****${digits.slice(-4)}`;
  };

  const formatPhone = (phone: string) => {
    if (!phone) return "";
    const digits = phone.replace(/\D/g, "");
    if (digits.length === 10) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
    }
    return phone;
  };

  const formatEIN = (ein: string) => {
    if (!ein) return "";
    const digits = ein.replace(/\D/g, "");
    if (digits.length === 9) {
      return `${digits.slice(0, 2)}-${digits.slice(2)}`;
    }
    return ein;
  };

  const businessTypes: Record<number, string> = {
    1: "Sole Proprietor",
    2: "Corporation",
    3: "Limited Liability Company",
    4: "Partnership",
  };

  const accountMethods: Record<number, string> = {
    10: "Corporate Checking",
    11: "Corporate Savings",
    20: "Personal Checking",
    21: "Personal Savings",
  };

  const member = formData.merchant?.members?.[0];
  const account = formData.accounts?.[0];

  if (submitSuccess) {
    const userAccount = submitResult?.data?.userAccount;

    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 py-12 px-8">
            <div className="text-center mb-8">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-2">Application Submitted Successfully!</h1>
              <p className="text-gray-600 mb-6">Your merchant onboarding application has been submitted for review.</p>
            </div>

            {userAccount && userAccount.created && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <div className="flex items-start space-x-3">
                  <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                  <div>
                    <h3 className="text-sm font-medium text-blue-900 mb-2">Payrix Portal Account Created</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <p>
                        <strong>Username:</strong> {userAccount.username}
                      </p>
                      <p>
                        <strong>Email:</strong> {userAccount.email}
                      </p>
                      <p className="mt-3">
                        <strong>Portal URL:</strong>{" "}
                        <a
                          href="https://test-portal.payrix.com/dashboard"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          https://test-portal.payrix.com/dashboard
                        </a>
                      </p>
                      <p className="mt-3">You can now log in to your Payrix portal using these credentials to:</p>
                      <ul className="list-disc list-inside ml-4 space-y-1">
                        <li>View transaction history and reports</li>
                        <li>Manage account settings</li>
                        <li>Access payment processing tools</li>
                        <li>Monitor account status</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3">What happens next?</h3>
              <div className="text-sm text-gray-700 space-y-2">
                <p>• Your application will be reviewed within 1-2 business days</p>
                <p>• You will receive email notifications about your application status</p>
                <p>• Additional documentation may be requested during the review process</p>
                {userAccount && userAccount.created && <p>• Access your Payrix portal using the credentials above</p>}
                <p>• Contact support if you have any questions</p>
              </div>
            </div>

            <div className="text-center">
              <button
                onClick={() => (window.location.href = "/")}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Return to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Review & Submit</h1>
            <p className="text-gray-600 mt-1">Please review all information before submitting your application</p>
          </div>

          <div className="px-8 py-8">
            {/* Business Information Section */}
            <div className="mb-10">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-medium text-gray-900">Business Information</h2>
              </div>
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Legal Business Name</dt>
                    <dd className="text-sm text-gray-900 mt-1">{formData.name || "—"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">DBA</dt>
                    <dd className="text-sm text-gray-900 mt-1">{formData.merchant?.dba || "—"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Business Type</dt>
                    <dd className="text-sm text-gray-900 mt-1">{formData.type ? businessTypes[formData.type] : "—"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Tax ID/EIN</dt>
                    <dd className="text-sm text-gray-900 mt-1">{formatEIN(formData.ein || "")}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="text-sm text-gray-900 mt-1">{formData.email || "—"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Phone</dt>
                    <dd className="text-sm text-gray-900 mt-1">{formatPhone(formData.phone || "")}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Website</dt>
                    <dd className="text-sm text-gray-900 mt-1">{formData.website || "—"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">MCC Code</dt>
                    <dd className="text-sm text-gray-900 mt-1">{formData.merchant?.mcc || "—"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Established Date</dt>
                    <dd className="text-sm text-gray-900 mt-1">
                      {formData.merchant?.established
                        ? formData.merchant.established.length === 8
                          ? `${formData.merchant.established.slice(4, 6)}/${formData.merchant.established.slice(
                              6,
                              8
                            )}/${formData.merchant.established.slice(0, 4)}`
                          : formData.merchant.established
                        : "—"}
                    </dd>
                  </div>
                  <div className="md:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Business Address</dt>
                    <dd className="text-sm text-gray-900 mt-1">
                      {[formData.address1, formData.address2, `${formData.city}, ${formData.state} ${formData.zip}`, formData.country]
                        .filter(Boolean)
                        .join(", ") || "—"}
                    </dd>
                  </div>
                </div>
              </div>
            </div>

            {/* Owner Information Section */}
            {member && (
              <div className="mb-10">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-medium text-gray-900">Owner Information</h2>
                </div>
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Full Name</dt>
                      <dd className="text-sm text-gray-900 mt-1">
                        {`${member.first || ""} ${member.middle ? member.middle + " " : ""}${member.last || ""}`.trim() || "—"}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Title</dt>
                      <dd className="text-sm text-gray-900 mt-1">{member.title || "—"}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Date of Birth</dt>
                      <dd className="text-sm text-gray-900 mt-1">
                        {member.dob
                          ? member.dob.length === 8
                            ? `${member.dob.slice(4, 6)}/${member.dob.slice(6, 8)}/${member.dob.slice(0, 4)}`
                            : member.dob
                          : "—"}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">SSN</dt>
                      <dd className="text-sm text-gray-900 mt-1">{formatSSN(member.ssn || "")}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Email</dt>
                      <dd className="text-sm text-gray-900 mt-1">{member.email || "—"}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Phone</dt>
                      <dd className="text-sm text-gray-900 mt-1">{formatPhone(member.phone || "")}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Ownership Percentage</dt>
                      <dd className="text-sm text-gray-900 mt-1">{Math.round((member.ownership || 0) / 100)}%</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Driver&apos;s License</dt>
                      <dd className="text-sm text-gray-900 mt-1">{member.dl ? `${member.dl} (${member.dlstate || ""})` : "Not provided"}</dd>
                    </div>
                    <div className="md:col-span-2">
                      <dt className="text-sm font-medium text-gray-500">Personal Address</dt>
                      <dd className="text-sm text-gray-900 mt-1">
                        {[member.address1, member.address2, `${member.city}, ${member.state} ${member.zip}`, member.country]
                          .filter(Boolean)
                          .join(", ") || "—"}
                      </dd>
                    </div>
                    <div className="md:col-span-2">
                      <dt className="text-sm font-medium text-gray-500">Responsibilities & Status</dt>
                      <dd className="text-sm text-gray-900 mt-1">
                        <div className="flex flex-wrap gap-2">
                          {member.significantResponsibility === 1 && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Significant Responsibility
                            </span>
                          )}
                          {member.politicallyExposed === 1 && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Politically Exposed
                            </span>
                          )}
                          {member.significantResponsibility !== 1 && member.politicallyExposed !== 1 && <span className="text-gray-500">None</span>}
                        </div>
                      </dd>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Bank Account Information Section */}
            {account && (
              <div className="mb-10">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-medium text-gray-900">Bank Account Information</h2>
                </div>
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Account Type</dt>
                      <dd className="text-sm text-gray-900 mt-1">{account.account.method ? accountMethods[account.account.method] : "—"}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Primary Account</dt>
                      <dd className="text-sm text-gray-900 mt-1">{account.primary === 1 ? "Yes" : "No"}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Routing Number</dt>
                      <dd className="text-sm text-gray-900 mt-1">{account.account.routing || "—"}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Account Number</dt>
                      <dd className="text-sm text-gray-900 mt-1">{formatAccountNumber(account.account.number || "")}</dd>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Terms & Conditions Acceptance */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Terms & Conditions</h2>
              <div className="space-y-4">
                <div
                  className={`bg-gray-50 border rounded-lg p-6 ${validationErrors.tcAttestation ? "border-red-300 bg-red-50" : "border-gray-200"}`}
                >
                  <label className="flex items-start cursor-pointer">
                    <input
                      type="checkbox"
                      checked={tcAttestation}
                      onChange={(e) => {
                        setTcAttestation(e.target.checked);
                        if (e.target.checked && validationErrors.tcAttestation) {
                          setValidationErrors((prev) => ({ ...prev, tcAttestation: undefined }));
                        }
                      }}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">
                      I accept the Payrix Terms and Conditions (version 1.0) and authorize the processing of this merchant application. I certify that
                      all information provided is accurate and complete.
                    </span>
                  </label>
                  {validationErrors.tcAttestation && (
                    <div className="mt-2 text-sm text-red-600 flex items-center">
                      <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {validationErrors.tcAttestation}
                    </div>
                  )}
                </div>

                <div
                  className={`bg-gray-50 border rounded-lg p-6 ${validationErrors.visaDisclosure ? "border-red-300 bg-red-50" : "border-gray-200"}`}
                >
                  <label className="flex items-start cursor-pointer">
                    <input
                      type="checkbox"
                      checked={visaDisclosure}
                      onChange={(e) => {
                        setVisaDisclosure(e.target.checked);
                        if (e.target.checked && validationErrors.visaDisclosure) {
                          setValidationErrors((prev) => ({ ...prev, visaDisclosure: undefined }));
                        }
                      }}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">
                      I acknowledge the Visa disclosure requirements and confirm that all payment processing will comply with card network
                      regulations. I understand that false or misleading information may result in account termination.
                    </span>
                  </label>
                  {validationErrors.visaDisclosure && (
                    <div className="mt-2 text-sm text-red-600 flex items-center">
                      <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {validationErrors.visaDisclosure}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Important Notice */}
            <div className="mb-10">
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-amber-900 mb-1">Important Notice</h3>
                    <div className="text-sm text-amber-800 space-y-1">
                      <p>• By submitting this application, you certify that all information provided is accurate and complete</p>
                      <p>• Your application will be reviewed within 1-2 business days</p>
                      <p>• Additional documentation may be requested during the review process</p>
                      <p>• You will receive email notifications about your application status</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Error Display */}
            {submitError && (
              <div className="mb-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <svg className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <h3 className="text-sm font-medium text-red-900 mb-1">Submission Error</h3>
                      <p className="text-sm text-red-800">{typeof submitError === "string" ? submitError : "An error occurred"}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                disabled={isSubmitting}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isSubmitting && (
                  <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                )}
                <span>{isSubmitting ? "Submitting..." : "Submit Application"}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingReview;
